<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Password Generator</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>

<body class="min-h-screen flex items-center justify-center text-white" style="background-color: #003153; background-image: linear-gradient(315deg, #003153 0%, #1B1B1B 74%);">

<div
        x-data="passwordGenerator()"
        x-init="init()"
        class="relative bg-white/10 p-8 rounded-2xl shadow-2xl backdrop-blur-md w-full max-w-xl space-y-6 transition-all duration-300 ease-in-out transform hover:scale-[1.02]"
>

    <!-- Toaster -->
    <div
            x-show="showToast"
            x-transition
            x-cloak
            class="absolute top-3 right-3 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg text-sm font-semibold"
    >
        Password Copied!
    </div>

    <h1 class="text-2xl font-bold text-center">🔐 Password Generator</h1>

    <!-- Password Output -->
    <div
            class="bg-white/20 rounded-md p-4 flex justify-between items-center text-lg font-mono transition-all duration-300"
    >
        <span x-text="password" class="break-all"></span>
        <button
                @click="copyToClipboard()"
                class="ml-2 text-green-300 hover:text-green-100 text-sm"
                title="Copy to Clipboard"
        >📋</button>
    </div>

    <!-- Length -->
    <div>
        <label class="block text-sm font-semibold mb-1">
            Length: <span x-text="length"></span>
        </label>
        <input type="range" min="4" max="32" x-model="length" class="w-full accent-yellow-400">
    </div>

    <!-- Options -->
    <div class="grid grid-cols-2 gap-4 text-sm">
        <label class="flex items-center gap-2">
            <input type="checkbox" x-model="useUpper" class="accent-white"> Uppercase
        </label>
        <label class="flex items-center gap-2">
            <input type="checkbox" x-model="useLower" class="accent-white"> Lowercase
        </label>
        <label class="flex items-center gap-2">
            <input type="checkbox" x-model="useNumbers" class="accent-white"> Numbers
        </label>
        <label class="flex items-center gap-2">
            <input type="checkbox" x-model="useSymbols" class="accent-white"> Symbols
        </label>

    </div>

    <!-- Generate Button -->
    <button
            @click="generatePassword()"
            class="w-full mt-4 py-2 bg-yellow-400 text-gray-800 font-bold rounded-md hover:bg-yellow-300 transition transform hover:scale-[1.02]"
    >
        🔁 Generate Password
    </button>


    <!-- Footer -->
    <div class="mt-12 text-center">
        <div class="gradient-bg text-white py-4 px-8 rounded-2xl shadow-lg inline-block">
            <p class="text-sm flex items-center justify-center gap-2">
                <span>Developed with</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-300" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                </svg>
                <span>by</span>
                <a href="https://tanvirhossenbappy.com/" target="_blank" class="font-bold hover:underline transition-all flex items-center">
                    Tanvir Hossen Bappy
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                        <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                    </svg>
                </a>
            </p>
        </div>
    </div>


</div>

<script>
    function passwordGenerator() {
        return {
            length: 12,
            useUpper: true,
            useLower: true,
            useNumbers: true,
            useSymbols: false,
            password: '',
            showToast: false,

            generatePassword() {
                const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                const lower = 'abcdefghijklmnopqrstuvwxyz';
                const numbers = '0123456789';
                const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
                let charset = '';

                if (this.useUpper) charset += upper;
                if (this.useLower) charset += lower;
                if (this.useNumbers) charset += numbers;
                if (this.useSymbols) charset += symbols;

                if (!charset) {
                    this.password = 'Please select at least one option!';
                    return;
                }

                this.password = Array.from({ length: this.length }, () =>
                    charset[Math.floor(Math.random() * charset.length)]
                ).join('');
            },

            copyToClipboard() {
                navigator.clipboard.writeText(this.password).then(() => {
                    this.showToast = true;
                    setTimeout(() => this.showToast = false, 1500);
                });
            },

            init() {
                this.generatePassword();
            }
        }
    }
</script>

</body>
</html>
