<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Tracker - Alpine.js</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#10b981',
                        secondary: '#059669',
                        accent: '#34d399',
                        dark: '#1e293b',
                        expense: '#ef4444',
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #34d399 100%);
            background-size: 200% 200%;
            animation: gradientAnimation 10s ease infinite;
        }

        @keyframes gradientAnimation {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .expense-card {
            transition: all 0.3s ease;
        }

        .expense-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .category-food { border-left-color: #f59e0b; }
        .category-transport { border-left-color: #3b82f6; }
        .category-entertainment { border-left-color: #8b5cf6; }
        .category-shopping { border-left-color: #ec4899; }
        .category-bills { border-left-color: #ef4444; }
        .category-health { border-left-color: #10b981; }
        .category-other { border-left-color: #6b7280; }

        /* Toast Styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            pointer-events: none;
        }

        .toast {
            pointer-events: auto;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease-in-out;
            margin-bottom: 10px;
            min-width: 300px;
            max-width: 400px;
        }

        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-left: 4px solid #34d399;
        }

        .toast-error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border-left: 4px solid #f87171;
        }

        .toast-slide-enter {
            transform: translateX(100%);
            opacity: 0;
        }

        .toast-slide-enter-active {
            transform: translateX(0);
            opacity: 1;
            transition: all 0.3s ease-out;
        }

        .toast-slide-leave {
            transform: translateX(0);
            opacity: 1;
        }

        .toast-slide-leave-active {
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease-in;
        }
    </style>
</head>
<body class="bg-slate-100 min-h-screen py-8 bg-[url('https://www.transparenttextures.com/patterns/cubes.png')]">
<div class="container mx-auto px-4 max-w-6xl" x-data="{
        expenses: JSON.parse(localStorage.getItem('expenses') || '[]'),
        showAddModal: false,
        showStatsModal: false,
        toasts: [],
        newExpense: {
            amount: '',
            description: '',
            category: 'food',
            date: new Date().toISOString().split('T')[0]
        },
        categories: [
            { id: 'food', name: 'Food & Dining', icon: '🍽️', color: '#f59e0b' },
            { id: 'transport', name: 'Transportation', icon: '🚗', color: '#3b82f6' },
            { id: 'entertainment', name: 'Entertainment', icon: '🎬', color: '#8b5cf6' },
            { id: 'shopping', name: 'Shopping', icon: '🛍️', color: '#ec4899' },
            { id: 'bills', name: 'Bills & Utilities', icon: '💡', color: '#ef4444' },
            { id: 'health', name: 'Health & Fitness', icon: '🏥', color: '#10b981' },
            { id: 'other', name: 'Other', icon: '📝', color: '#6b7280' }
        ],
        filter: 'all',
        dateFilter: 'all',

        addExpense() {
            if (!this.newExpense.amount || !this.newExpense.description) return;

            this.expenses.push({
                id: Date.now(),
                amount: parseFloat(this.newExpense.amount),
                description: this.newExpense.description,
                category: this.newExpense.category,
                date: this.newExpense.date,
                timestamp: new Date().toISOString()
            });

            this.newExpense = {
                amount: '',
                description: '',
                category: 'food',
                date: new Date().toISOString().split('T')[0]
            };

            this.showAddModal = false;
            this.saveExpenses();
            this.showToast('Expense added successfully!', 'success');
        },

        deleteExpense(id) {
            const expense = this.expenses.find(exp => exp.id === id);
            this.expenses = this.expenses.filter(expense => expense.id !== id);
            this.saveExpenses();
            this.showToast(`Deleted "${expense.description}"`, 'error');
        },

        showToast(message, type = 'success') {
            const toast = {
                id: Date.now(),
                message: message,
                type: type,
                show: false
            };

            this.toasts.push(toast);

            // Trigger animation
            setTimeout(() => {
                toast.show = true;
            }, 100);

            // Auto remove after 3 seconds
            setTimeout(() => {
                this.removeToast(toast.id);
            }, 3000);
        },

        removeToast(id) {
            const toast = this.toasts.find(t => t.id === id);
            if (toast) {
                toast.show = false;
                setTimeout(() => {
                    this.toasts = this.toasts.filter(t => t.id !== id);
                }, 300);
            }
        },

        saveExpenses() {
            localStorage.setItem('expenses', JSON.stringify(this.expenses));
        },

        filteredExpenses() {
            let filtered = this.expenses;

            if (this.filter !== 'all') {
                filtered = filtered.filter(expense => expense.category === this.filter);
            }

            if (this.dateFilter !== 'all') {
                const now = new Date();
                const filterDate = new Date();

                switch(this.dateFilter) {
                    case 'today':
                        filterDate.setHours(0, 0, 0, 0);
                        filtered = filtered.filter(expense => new Date(expense.date) >= filterDate);
                        break;
                    case 'week':
                        filterDate.setDate(now.getDate() - 7);
                        filtered = filtered.filter(expense => new Date(expense.date) >= filterDate);
                        break;
                    case 'month':
                        filterDate.setMonth(now.getMonth() - 1);
                        filtered = filtered.filter(expense => new Date(expense.date) >= filterDate);
                        break;
                }
            }

            return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
        },

        getTotalExpenses() {
            return this.filteredExpenses().reduce((total, expense) => total + expense.amount, 0);
        },

        getCategoryName(categoryId) {
            const category = this.categories.find(cat => cat.id === categoryId);
            return category ? category.name : 'Unknown';
        },

        getCategoryIcon(categoryId) {
            const category = this.categories.find(cat => cat.id === categoryId);
            return category ? category.icon : '📝';
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    }">

    <!-- Header -->
    <div class="gradient-bg p-6 rounded-xl shadow-lg mb-8 relative overflow-hidden">
        <div class="absolute -right-6 -top-6 opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-32 w-32" viewBox="0 0 20 20" fill="currentColor">
                <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" />
            </svg>
        </div>
        <div class="flex flex-col items-center justify-center relative z-10">
            <div class="floating mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white opacity-90" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-center text-white">Expense Tracker</h1>
            <p class="text-white text-center text-sm opacity-80 mt-2">Track your spending and stay on budget</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Expenses</p>
                    <p class="text-2xl font-bold text-expense" x-text="formatCurrency(getTotalExpenses())"></p>
                </div>
                <div class="bg-red-100 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-expense" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Transactions</p>
                    <p class="text-2xl font-bold text-primary" x-text="filteredExpenses().length"></p>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Average per Transaction</p>
                    <p class="text-2xl font-bold text-secondary" x-text="formatCurrency(filteredExpenses().length > 0 ? getTotalExpenses() / filteredExpenses().length : 0)"></p>
                </div>
                <div class="bg-blue-100 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 mb-6">
        <button
                @click="showAddModal = true"
                class="flex-1 gradient-bg text-white px-6 py-3 rounded-lg hover:shadow-lg transition duration-300 font-semibold flex items-center justify-center gap-2 shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Add Expense
        </button>

        <button
                @click="showStatsModal = true"
                class="flex-1 bg-white text-primary border-2 border-primary px-6 py-3 rounded-lg hover:bg-primary hover:text-white transition duration-300 font-semibold flex items-center justify-center gap-2 shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            View Charts
        </button>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-xl shadow-md mb-6">
        <div class="flex flex-col lg:flex-row gap-4">
            <!-- Category Filter -->
            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Category</label>
                <select x-model="filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                    <option value="all">All Categories</option>
                    <template x-for="category in categories" :key="category.id">
                        <option :value="category.id" x-text="category.icon + ' ' + category.name"></option>
                    </template>
                </select>
            </div>

            <!-- Date Filter -->
            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Date</label>
                <select x-model="dateFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">Last 7 Days</option>
                    <option value="month">Last 30 Days</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Expense List -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-8">
        <h2 class="text-xl font-bold text-dark mb-4 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
            </svg>
            Recent Expenses
        </h2>

        <div x-show="filteredExpenses().length === 0" class="text-center py-8">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-300 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p class="text-gray-500 text-lg">No expenses found</p>
            <p class="text-gray-400 text-sm">Add your first expense to get started</p>
        </div>

        <div class="space-y-4">
            <template x-for="expense in filteredExpenses()" :key="expense.id">
                <div class="expense-card bg-gray-50 p-4 rounded-lg border-l-4" :class="'category-' + expense.category">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            <div class="text-2xl" x-text="getCategoryIcon(expense.category)"></div>
                            <div>
                                <h3 class="font-semibold text-dark" x-text="expense.description"></h3>
                                <p class="text-sm text-gray-500">
                                    <span x-text="getCategoryName(expense.category)"></span> •
                                    <span x-text="formatDate(expense.date)"></span>
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <span class="text-xl font-bold text-expense" x-text="formatCurrency(expense.amount)"></span>
                            <button
                                    @click="deleteExpense(expense.id)"
                                    class="text-gray-400 hover:text-red-500 transition-colors duration-300 p-2 rounded-full hover:bg-red-50">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Add Expense Modal -->
    <div
            x-show="showAddModal"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-90"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-90"
            class="fixed inset-0 z-50 overflow-y-auto"
            style="display: none;">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="fixed inset-0 bg-black opacity-40"></div>
            <div class="relative bg-white rounded-xl p-8 max-w-md w-full shadow-2xl border-t-4 border-primary">
                <h2 class="text-2xl font-bold mb-6 text-dark flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Add New Expense
                </h2>
                <form @submit.prevent="addExpense()">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount ($)</label>
                        <input
                                type="number"
                                step="0.01"
                                x-model="newExpense.amount"
                                placeholder="0.00"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-dark placeholder-gray-400"
                                required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <input
                                type="text"
                                x-model="newExpense.description"
                                placeholder="What did you spend on?"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-dark placeholder-gray-400"
                                required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select x-model="newExpense.category" class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-dark">
                            <template x-for="category in categories" :key="category.id">
                                <option :value="category.id" x-text="category.icon + ' ' + category.name"></option>
                            </template>
                        </select>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                        <input
                                type="date"
                                x-model="newExpense.date"
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-dark"
                                required>
                    </div>

                    <div class="flex justify-end gap-3">
                        <button
                                type="button"
                                @click="showAddModal = false"
                                class="px-5 py-2 bg-gray-100 text-dark rounded-lg hover:bg-gray-200 transition duration-300 font-medium">
                            Cancel
                        </button>
                        <button
                                type="submit"
                                class="px-5 py-2 gradient-bg text-white rounded-lg hover:shadow-lg transition duration-300 font-medium">
                            Add Expense
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Charts Modal -->
    <div
            x-show="showStatsModal"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-90"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-90"
            class="fixed inset-0 z-50 overflow-y-auto"
            style="display: none;"
            x-init="
                $watch('showStatsModal', value => {
                    if (value && expenses.length > 0) {
                        setTimeout(() => {
                            // Category breakdown chart
                            const categoryData = {};
                            expenses.forEach(expense => {
                                categoryData[expense.category] = (categoryData[expense.category] || 0) + expense.amount;
                            });

                            const categoryChart = new Chart(document.getElementById('categoryChart'), {
                                type: 'doughnut',
                                data: {
                                    labels: Object.keys(categoryData).map(cat => getCategoryName(cat)),
                                    datasets: [{
                                        data: Object.values(categoryData),
                                        backgroundColor: Object.keys(categoryData).map(cat => {
                                            const category = categories.find(c => c.id === cat);
                                            return category ? category.color : '#6b7280';
                                        }),
                                        borderWidth: 2,
                                        borderColor: '#ffffff'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    plugins: {
                                        legend: {
                                            position: 'bottom'
                                        }
                                    }
                                }
                            });

                            // Monthly spending chart
                            const monthlyData = {};
                            expenses.forEach(expense => {
                                const month = new Date(expense.date).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
                                monthlyData[month] = (monthlyData[month] || 0) + expense.amount;
                            });

                            const monthlyChart = new Chart(document.getElementById('monthlyChart'), {
                                type: 'line',
                                data: {
                                    labels: Object.keys(monthlyData),
                                    datasets: [{
                                        label: 'Monthly Spending',
                                        data: Object.values(monthlyData),
                                        borderColor: '#10b981',
                                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                        borderWidth: 3,
                                        fill: true,
                                        tension: 0.4
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    plugins: {
                                        legend: {
                                            display: false
                                        }
                                    },
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            ticks: {
                                                callback: function(value) {
                                                    return '$' + value.toFixed(0);
                                                }
                                            }
                                        }
                                    }
                                }
                            });
                        }, 100);
                    }
                })
            ">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="fixed inset-0 bg-black opacity-40"></div>
            <div class="relative bg-white rounded-xl p-8 max-w-4xl w-full shadow-2xl border-t-4 border-primary">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-dark flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Spending Analytics
                    </h2>
                    <button
                            @click="showStatsModal = false"
                            class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div x-show="expenses.length === 0" class="text-center py-12">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-300 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p class="text-gray-500 text-lg">No data to display</p>
                    <p class="text-gray-400 text-sm">Add some expenses to see your spending analytics</p>
                </div>

                <div x-show="expenses.length > 0" class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-lg font-semibold text-dark mb-4">Spending by Category</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <canvas id="categoryChart" width="300" height="300"></canvas>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold text-dark mb-4">Monthly Spending Trend</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <canvas id="monthlyChart" width="300" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="mt-12 text-center">
        <div class="gradient-bg text-white py-4 px-8 rounded-2xl shadow-lg inline-block">
            <p class="text-sm flex items-center justify-center gap-2">
                <span>Developed with</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-300" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                </svg>
                <span>by</span>
                <a href="https://tanvirhossenbappy.com/" target="_blank" class="font-bold hover:underline transition-all flex items-center">
                    Tanvir Hossen Bappy
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                        <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                    </svg>
                </a>
            </p>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container">
        <template x-for="toast in toasts" :key="toast.id">
            <div
                class="toast rounded-lg shadow-lg p-4 text-white flex items-center justify-between"
                :class="[
                    toast.type === 'success' ? 'toast-success' : 'toast-error',
                    toast.show ? 'show' : ''
                ]"
                x-transition:enter="toast-slide-enter"
                x-transition:enter-start="toast-slide-enter"
                x-transition:enter-end="toast-slide-enter-active"
                x-transition:leave="toast-slide-leave"
                x-transition:leave-start="toast-slide-leave"
                x-transition:leave-end="toast-slide-leave-active">

                <div class="flex items-center gap-3">
                    <!-- Success Icon -->
                    <div x-show="toast.type === 'success'" class="flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>

                    <!-- Error Icon -->
                    <div x-show="toast.type === 'error'" class="flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </div>

                    <div>
                        <p class="font-medium" x-text="toast.message"></p>
                    </div>
                </div>

                <!-- Close Button -->
                <button
                    @click="removeToast(toast.id)"
                    class="flex-shrink-0 ml-4 text-white hover:text-gray-200 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </template>
    </div>
</div>
</body>
</html>