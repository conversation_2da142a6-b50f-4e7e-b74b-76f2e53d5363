<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Note Editor - Alpine.js</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        accent: '#d946ef',
                        dark: '#1e293b',
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%);
            background-size: 200% 200%;
            animation: gradientAnimation 10s ease infinite;
        }

        @keyframes gradientAnimation {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .markdown-preview {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }

        .markdown-preview h1 { @apply text-3xl font-bold mb-4 text-gray-800 border-b-2 border-gray-200 pb-2; }
        .markdown-preview h2 { @apply text-2xl font-bold mb-3 text-gray-800 border-b border-gray-200 pb-1; }
        .markdown-preview h3 { @apply text-xl font-bold mb-2 text-gray-800; }
        .markdown-preview h4 { @apply text-lg font-bold mb-2 text-gray-800; }
        .markdown-preview h5 { @apply text-base font-bold mb-2 text-gray-800; }
        .markdown-preview h6 { @apply text-sm font-bold mb-2 text-gray-800; }
        .markdown-preview p { @apply mb-4 text-gray-700 leading-relaxed; }
        .markdown-preview ul { @apply mb-4 pl-6 list-disc text-gray-700; }
        .markdown-preview ol { @apply mb-4 pl-6 list-decimal text-gray-700; }
        .markdown-preview li { @apply mb-1; }
        .markdown-preview blockquote { @apply border-l-4 border-primary pl-4 italic text-gray-600 mb-4; }
        .markdown-preview code { @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono text-red-600; }
        .markdown-preview pre { @apply bg-gray-100 p-4 rounded-lg mb-4 overflow-x-auto; }
        .markdown-preview pre code { @apply bg-transparent px-0 py-0 text-gray-800; }
        .markdown-preview a { @apply text-primary hover:text-secondary underline; }
        .markdown-preview table { @apply w-full border-collapse border border-gray-300 mb-4; }
        .markdown-preview th { @apply border border-gray-300 px-4 py-2 bg-gray-100 font-bold; }
        .markdown-preview td { @apply border border-gray-300 px-4 py-2; }
        .markdown-preview hr { @apply border-t-2 border-gray-200 my-6; }

        .resizer {
            width: 4px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            cursor: col-resize;
            position: relative;
        }

        .resizer:hover {
            background: linear-gradient(135deg, #8b5cf6 0%, #d946ef 100%);
        }

        .editor-textarea {
            resize: none;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .note-item {
            transition: all 0.3s ease;
        }

        .note-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-slate-100 min-h-screen bg-[url('https://www.transparenttextures.com/patterns/cubes.png')]">
<div class="container mx-auto px-4 py-6 max-w-7xl" x-data="markdownEditor()">
    <!-- Header -->
    <div class="gradient-bg p-6 rounded-xl shadow-lg mb-6 relative overflow-hidden">
        <div class="absolute -right-6 -top-6 opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-32 w-32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
        </div>
        <div class="flex flex-col md:flex-row items-center justify-between relative z-10">
            <div class="flex items-center gap-4 mb-4 md:mb-0">
                <div class="floating">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white opacity-90" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white">Markdown Editor</h1>
                    <p class="text-white text-sm opacity-80">Write and preview markdown in real-time</p>
                </div>
            </div>
            <div class="flex gap-2">
                <button
                    @click="createNewNote()"
                    class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition duration-300 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    New Note
                </button>
                <button
                    @click="showNotesList = !showNotesList"
                    class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 transition duration-300 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                    </svg>
                    Notes (<span x-text="notes.length"></span>)
                </button>
            </div>
        </div>
    </div>

    <!-- Notes List Modal -->
    <div
        x-show="showNotesList"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-90"
        x-transition:enter-end="opacity-100 transform scale-100"
        x-transition:leave="transition ease-in duration-300"
        x-transition:leave-start="opacity-100 transform scale-100"
        x-transition:leave-end="opacity-0 transform scale-90"
        class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none;">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="fixed inset-0 bg-black opacity-40" @click="showNotesList = false"></div>
            <div class="relative bg-white rounded-xl p-6 max-w-2xl w-full shadow-2xl max-h-[80vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-dark">Your Notes</h2>
                    <button @click="showNotesList = false" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-3">
                    <template x-for="note in notes" :key="note.id">
                        <div class="note-item bg-gray-50 p-4 rounded-lg border-l-4 border-primary cursor-pointer hover:bg-gray-100"
                             @click="loadNote(note.id); showNotesList = false">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-dark mb-1" x-text="note.title || 'Untitled Note'"></h3>
                                    <p class="text-gray-600 text-sm mb-2" x-text="note.content.substring(0, 100) + (note.content.length > 100 ? '...' : '')"></p>
                                    <p class="text-xs text-gray-400" x-text="new Date(note.updatedAt).toLocaleString()"></p>
                                </div>
                                <button @click.stop="deleteNote(note.id)" class="text-gray-400 hover:text-red-500 transition-colors p-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </template>
                    <div x-show="notes.length === 0" class="text-center py-8 text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p>No notes yet. Create your first note!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Note Title -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-4">
        <input
            type="text"
            x-model="currentNote.title"
            @input="saveCurrentNote()"
            placeholder="Note title..."
            class="w-full text-xl font-semibold border-none outline-none bg-transparent text-dark placeholder-gray-400">
    </div>

    <!-- Editor Layout -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden" style="height: 70vh;">
        <div class="flex h-full">
            <!-- Editor Panel -->
            <div class="flex-1 flex flex-col" :style="`width: ${editorWidth}%`">
                <div class="bg-gray-50 px-4 py-2 border-b flex items-center justify-between">
                    <h3 class="font-semibold text-dark flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Markdown Editor
                    </h3>
                    <div class="flex items-center gap-2 text-sm text-gray-500">
                        <span x-text="currentNote.content.length"></span> characters
                    </div>
                </div>
                <textarea
                    x-model="currentNote.content"
                    @input="saveCurrentNote(); updatePreview()"
                    placeholder="Start writing your markdown here...

# Welcome to Markdown Editor

## Features
- **Live preview** as you type
- **Auto-save** to local storage
- **Multiple notes** support
- **Responsive design**

### Markdown Syntax Examples

**Bold text** and *italic text*

- Bullet point 1
- Bullet point 2

1. Numbered list
2. Another item

> This is a blockquote

`inline code` and code blocks:

```javascript
function hello() {
    console.log('Hello, World!');
}
```

[Link to website](https://example.com)

---

Happy writing! 🚀"
                    class="flex-1 p-4 border-none outline-none resize-none editor-textarea text-gray-800 leading-relaxed"
                    style="font-size: 14px; line-height: 1.6;"></textarea>
            </div>

            <!-- Resizer -->
            <div class="resizer" @mousedown="startResize($event)"></div>

            <!-- Preview Panel -->
            <div class="flex-1 flex flex-col" :style="`width: ${100 - editorWidth}%`">
                <div class="bg-gray-50 px-4 py-2 border-b">
                    <h3 class="font-semibold text-dark flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        Live Preview
                    </h3>
                </div>
                <div class="flex-1 p-4 overflow-y-auto markdown-preview" x-html="previewHtml"></div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="mt-8 text-center">
        <div class="gradient-bg text-white py-4 px-8 rounded-2xl shadow-lg inline-block">
            <p class="text-sm flex items-center justify-center gap-2">
                <span>Developed with</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-300" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                </svg>
                <span>by</span>
                <a href="https://tanvirhossenbappy.com/" target="_blank" class="font-bold hover:underline transition-all flex items-center">
                    Tanvir Hossen Bappy
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                        <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
                    </svg>
                </a>
            </p>
        </div>
    </div>
</div>

<script>
    function markdownEditor() {
        return {
            notes: JSON.parse(localStorage.getItem('markdownNotes') || '[]'),
            currentNote: {
                id: null,
                title: '',
                content: '',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            previewHtml: '',
            showNotesList: false,
            editorWidth: 50,
            isResizing: false,

            init() {
                // Load the last edited note or create a new one
                const lastNoteId = localStorage.getItem('lastEditedNoteId');
                if (lastNoteId && this.notes.find(note => note.id === lastNoteId)) {
                    this.loadNote(lastNoteId);
                } else if (this.notes.length > 0) {
                    this.loadNote(this.notes[0].id);
                } else {
                    this.createNewNote();
                }

                this.updatePreview();

                // Add resize event listeners
                document.addEventListener('mousemove', (e) => this.handleResize(e));
                document.addEventListener('mouseup', () => this.stopResize());
            },

            createNewNote() {
                const newNote = {
                    id: Date.now().toString(),
                    title: '',
                    content: '',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                this.notes.unshift(newNote);
                this.currentNote = { ...newNote };
                this.saveNotes();
                this.updatePreview();
                localStorage.setItem('lastEditedNoteId', newNote.id);
            },

            loadNote(noteId) {
                const note = this.notes.find(n => n.id === noteId);
                if (note) {
                    this.currentNote = { ...note };
                    this.updatePreview();
                    localStorage.setItem('lastEditedNoteId', noteId);
                }
            },

            saveCurrentNote() {
                if (!this.currentNote.id) return;

                this.currentNote.updatedAt = new Date().toISOString();

                const noteIndex = this.notes.findIndex(note => note.id === this.currentNote.id);
                if (noteIndex !== -1) {
                    this.notes[noteIndex] = { ...this.currentNote };
                    this.saveNotes();
                }
            },

            deleteNote(noteId) {
                if (confirm('Are you sure you want to delete this note?')) {
                    this.notes = this.notes.filter(note => note.id !== noteId);
                    this.saveNotes();

                    if (this.currentNote.id === noteId) {
                        if (this.notes.length > 0) {
                            this.loadNote(this.notes[0].id);
                        } else {
                            this.createNewNote();
                        }
                    }
                }
            },

            saveNotes() {
                localStorage.setItem('markdownNotes', JSON.stringify(this.notes));
            },

            updatePreview() {
                try {
                    this.previewHtml = marked.parse(this.currentNote.content || '');
                } catch (error) {
                    this.previewHtml = '<p class="text-red-500">Error parsing markdown</p>';
                }
            },

            startResize(e) {
                this.isResizing = true;
                e.preventDefault();
            },

            handleResize(e) {
                if (!this.isResizing) return;

                const container = e.target.closest('.bg-white.rounded-xl.shadow-lg');
                if (!container) return;

                const containerRect = container.getBoundingClientRect();
                const mouseX = e.clientX - containerRect.left;
                const newWidth = (mouseX / containerRect.width) * 100;

                // Constrain between 20% and 80%
                this.editorWidth = Math.max(20, Math.min(80, newWidth));
            },

            stopResize() {
                this.isResizing = false;
            }
        }
    }
</script>

</body>
</html>
